package com.example.financialindicatordaemon.config;

import com.example.financialindicatordaemon.client.CoinMarketCapApiClient;
import com.example.financialindicatordaemon.client.IndicatorApiClient;
import com.example.financialindicatordaemon.client.MockCoinMarketCapApiClient;
import com.example.financialindicatordaemon.client.MockIndicatorClient;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

@TestConfiguration
public class TestConfig {

    @Bean
    @Primary
    public CoinMarketCapApiClient coinMarketCapApiClient() {
        return new MockCoinMarketCapApiClient();
    }

    @Bean
    @Primary
    public IndicatorApiClient indicatorApiClient() {
        return new MockIndicatorClient();
    }
}
