package com.example.financialindicatordaemon.service;

import com.example.financialindicatordaemon.BaseTest;
import com.example.financialindicatordaemon.client.IndicatorData;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;

public class IndicatorDataServiceTest extends BaseTest {

    @Autowired
    private IndicatorDataService indicatorDataService;
    @Autowired
    private DataMiningService dataMiningService;

    @Test
    public void calculateIndicators_shouldStore() {
        dataMiningService.mineMappings();
        dataMiningService.mineSymbols(List.of("SOL"), 2781);
        indicatorDataService.calculateIndicators("SOL", "USD");
        List<IndicatorData> indicatorData = indicatorDataService.find("SOL", "USD");
        assertThat(indicatorData).isNotNull();
        assertThat(indicatorData).isNotEmpty();
        assertThat(indicatorData.size()).isEqualTo(1893);
    }

}
