package com.example.financialindicatordaemon;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;

@SpringBootApplication
@MapperScan("com.example.financialindicatordaemon.mapper")
@ConfigurationPropertiesScan
public class FinancialIndicatorDaemonApplication {

    public static void main(String[] args) {
        SpringApplication.run(FinancialIndicatorDaemonApplication.class, args);
    }

}
