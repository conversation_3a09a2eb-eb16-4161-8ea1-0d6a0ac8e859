package com.example.financialindicatordaemon.amqp.listener;

import com.example.financialindicatordaemon.config.RabbitMqConfig;
import com.example.financialindicatordaemon.amqp.SymbolsMessage;
import com.example.financialindicatordaemon.service.DataMiningService;
import com.example.financialindicatordaemon.service.DataTransformationService;
import com.rabbitmq.client.Channel;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

@Component
public class MineUsdDataListener extends BaseRabbitMqListener {

    public MineUsdDataListener(DataMiningService dataMiningService,
                               DataTransformationService dataTransformationService) {
        super(dataMiningService, dataTransformationService);
    }

    @RabbitListener(queues = RabbitMqConfig.MINE_USD_DATA_BY_SYMBOLS)
    public void handleMineUsdDataBySymbols(@Payload SymbolsMessage message,
                                           Channel channel,
                                           @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag,
                                           Message amqpMessage) {
        handleSymbolsMessage(message, "usd", channel, deliveryTag, amqpMessage);
    }
}

