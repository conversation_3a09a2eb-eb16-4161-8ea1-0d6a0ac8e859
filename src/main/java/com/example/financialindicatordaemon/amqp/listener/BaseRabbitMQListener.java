package com.example.financialindicatordaemon.amqp.listener;

import com.example.financialindicatordaemon.config.AppConfig;
import com.example.financialindicatordaemon.dto.SymbolsMessage;
import com.example.financialindicatordaemon.service.DataMiningService;
import com.example.financialindicatordaemon.service.DataTransformationService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;

import java.io.IOException;

public abstract class BaseRabbitMQListener {

    private static final Logger logger = LoggerFactory.getLogger(BaseRabbitMQListener.class);

    protected final DataMiningService dataMiningService;
    protected final DataTransformationService dataTransformationService;

    public BaseRabbitMQListener(DataMiningService dataMiningService,
                                DataTransformationService dataTransformationService) {
        this.dataMiningService = dataMiningService;
        this.dataTransformationService = dataTransformationService;
    }

    protected void handleSymbolsMessage(SymbolsMessage message, String currency,
                                        Channel channel, long deliveryTag, Message amqpMessage) {
        try {
            channel.basicAck(deliveryTag, false);

            if (message.getSymbols() != null && !message.getSymbols().isEmpty()) {
                logger.info("Processing {} symbols for {}", message.getSymbols().size(), currency);
                dataMiningService.mineSymbols(message.getSymbols(),
                        "usd".equals(currency) ? AppConfig.USD_CURRENCY_ID : AppConfig.BTC_CURRENCY_ID);
            }
        } catch (Exception e) {
            logger.error("Message processing failed", e);
            try {
                channel.basicReject(deliveryTag, false);
            } catch (IOException ioException) {
                logger.error("Failed to reject message", ioException);
            }
        }
    }

    protected void handleSimpleMessage(String messageType, Channel channel, long deliveryTag,
                                       Message amqpMessage, Runnable processor) {
        try {
            channel.basicAck(deliveryTag, false);
            processor.run();
            logger.info("Processed {} message", messageType);
        } catch (Exception e) {
            logger.error("Failed to process {} message", messageType, e);
            try {
                channel.basicReject(deliveryTag, false);
            } catch (IOException ioException) {
                logger.error("Failed to reject message", ioException);
            }
        }
    }
}

