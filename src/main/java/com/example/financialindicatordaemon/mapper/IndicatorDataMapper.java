package com.example.financialindicatordaemon.mapper;

import com.example.financialindicatordaemon.client.IndicatorData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IndicatorDataMapper {

    IndicatorDataWrapper findLatestByCoinAndCurrency(@Param("symbol") String symbol,
                                                     @Param("conversionCurrency") String conversionCurrency);

    void insert(@Param("symbol") String symbol,
                @Param("conversionCurrency") String conversionCurrency,
                @Param("indicatorData") List<IndicatorData> indicatorData);
}
