package com.example.financialindicatordaemon.mapper;

import com.example.financialindicatordaemon.client.IndicatorData;

import java.util.List;

public class IndicatorDataWrapper {
    private List<IndicatorData> indicatorValues;

    public List<IndicatorData> getIndicatorValues() {
        return indicatorValues;
    }

    public void setIndicatorValues(List<IndicatorData> indicatorValues) {
        this.indicatorValues = indicatorValues;
    }
}
