package com.example.financialindicatordaemon.config;

import com.example.financialindicatordaemon.typehandler.IndicatorDataListTypeHandler;
import org.mybatis.spring.boot.autoconfigure.ConfigurationCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MyBatisConfiguration {

    @Bean
    ConfigurationCustomizer mybatisConfigurationCustomizer() {
        return (configuration) -> {
            // Register our custom type handler
            configuration.getTypeHandlerRegistry().register(IndicatorDataListTypeHandler.class);
        };
    }
}
