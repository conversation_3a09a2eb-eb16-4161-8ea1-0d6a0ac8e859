package com.example.financialindicatordaemon.config;

import com.baomidou.mybatisplus.autoconfigure.ConfigurationCustomizer;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.example.financialindicatordaemon.typehandler.IndicatorDataListTypeHandler;
import com.example.financialindicatordaemon.typehandler.QuoteTypeHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MyBatisPlusConfig {

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        return new MybatisPlusInterceptor();
    }

    @Bean
    ConfigurationCustomizer mybatisConfigurationCustomizer() {
        return configuration -> {
            configuration.getTypeHandlerRegistry().register(IndicatorDataListTypeHandler.class);
            configuration.getTypeHandlerRegistry().register(QuoteTypeHandler.class);
        };
    }
}
