package com.example.financialindicatordaemon.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "coinmarketcap-api", url = "https://pro-api.coinmarketcap.com")
public interface CoinMarketCapApiClient {

    @GetMapping("/v1/cryptocurrency/map")
    ResponseEntity<CryptocurrencyMappings> getCryptocurrencyMappings(
            @RequestHeader("X-CMC_PRO_API_KEY") String apiKey
    );

    @GetMapping("/data-api/v3.1/cryptocurrency/historical")
    ResponseEntity<CryptoCandleHistoricalQuotesResponse> getHistoricalQuotes(
            @RequestParam("id") String id,
            @RequestParam("timeStart") String timeStart,
            @RequestParam("timeEnd") String timeEnd,
            @RequestParam(value = "convertId", defaultValue = "2781") String convertId,
            @RequestParam(value = "interval", defaultValue = "1d") String interval
    );

}
