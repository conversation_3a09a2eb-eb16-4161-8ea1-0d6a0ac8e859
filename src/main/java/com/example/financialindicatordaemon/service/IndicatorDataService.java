package com.example.financialindicatordaemon.service;

import com.example.financialindicatordaemon.client.CalculateIndicatorsRequest;
import com.example.financialindicatordaemon.client.CryptoCandleHistoricalQuote;
import com.example.financialindicatordaemon.client.IndicatorApiClient;
import com.example.financialindicatordaemon.client.IndicatorData;
import com.example.financialindicatordaemon.dto.IndicatorDataWrapper;
import com.example.financialindicatordaemon.mapper.IndicatorDataMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class IndicatorDataService {

    private static final Logger logger = LoggerFactory.getLogger(IndicatorDataService.class);

    private final IndicatorApiClient indicatorApiClient;
    private final CmcCandleDataService cmcCandleDataService;
    private final IndicatorDataMapper indicatorDataMapper;

    public IndicatorDataService(@SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
                               IndicatorApiClient indicatorApiClient, CmcCandleDataService cmcCandleDataService,
                               IndicatorDataMapper indicatorDataMapper) {
        this.indicatorApiClient = indicatorApiClient;
        this.cmcCandleDataService = cmcCandleDataService;
        this.indicatorDataMapper = indicatorDataMapper;
    }

    public void calculateIndicators(String symbol, String conversionCurrency) {
        logger.info("Sending calculation request to indicator API");

        List<CryptoCandleHistoricalQuote> quotes = cmcCandleDataService.find(symbol, conversionCurrency);
        ResponseEntity<List<IndicatorData>> response = indicatorApiClient.calculateIndicators(quotes.stream()
                .map(
                        quote -> {
                            CalculateIndicatorsRequest request = new CalculateIndicatorsRequest();
                            request.setClose(quote.getQuote().getClose());
                            request.setHigh(quote.getQuote().getHigh());
                            request.setLow(quote.getQuote().getLow());
                            request.setMarketCap(quote.getQuote().getMarketCap());
                            request.setName(quote.getQuote().getName());
                            request.setOpen(quote.getQuote().getOpen());
                            request.setTimestamp(quote.getQuote().getTimestamp());
                            request.setVolume(quote.getQuote().getVolume());
                            return request;
                        }
                ).toList());

        if (!response.getStatusCode().is2xxSuccessful()) {
            throw new RuntimeException("Failed to calculate indicators: " + response.getStatusCode());
        }

        indicatorDataMapper.insert(symbol, conversionCurrency, response.getBody());
    }

    public List<IndicatorData> find(String symbol, String conversionCurrency) {
        IndicatorDataWrapper wrapper = indicatorDataMapper.findLatestByCoinAndCurrency(symbol, conversionCurrency);

        if (wrapper != null && wrapper.getIndicatorValues() != null) {
            return wrapper.getIndicatorValues();
        }

        logger.warn("No indicator data found for symbol: {} and currency: {}", symbol, conversionCurrency);
        return List.of();
    }

}
