server:
  port: 6601

spring:
  application:
    name: financial-indicator-daemon

  datasource:
    url: jdbc:postgresql://${POSTGRES_HOST:localhost}:${POSTGRES_PORT:5432}/${POSTGRES_DB:financial_indicator_db}
    username: ${POSTGRES_USER:financial_user}
    password: ${POSTGRES_PASSWORD:financial_pass}
    hikari:
      maximum-pool-size: 10

  liquibase:
    change-log: classpath:db/changelog/db.changelog-master.sql

  rabbitmq:
    host: ${RABBITMQ_HOST:localhost}
    port: ${RABBITMQ_PORT:5672}
    username: ${RABBITMQ_USER:guest}
    password: ${RABBITMQ_PASSWORD:guest}

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.example.financialindicatordaemon.entity

app:
  cmc:
    api-key: ${CMC_API_KEY:f8e302bb-197c-4a01-8fcf-136e9d334680}
    throttle:
      min: ${CMC_API_THROTTLE_MIN:300}
      max: ${CMC_API_THROTTLE_MAX:500}
    symbol-overrides: ${CMC_SYMBOL_OVERRIDES:}
  indicator-api:
    host: ${INDICATOR_API_HOST:http://localhost:5000}
  amount-of-coins-to-get-by-ranking: ${AMOUNT_OF_COINS_TO_GET_BY_RANKING:3}

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics

springdoc:
  api-docs:
    path: /docs/openapi.json
  swagger-ui:
    path: /docs

logging:
  level:
    com.example.financialindicatordaemon: INFO
