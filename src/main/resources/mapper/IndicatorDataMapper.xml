<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.financialindicatordaemon.mapper.IndicatorDataMapper">
    <insert id="insert">
        INSERT INTO crypto_data.indicator_data
        (symbol, conversion_currency, indicator_values)
        VALUES (#{symbol}, #{conversionCurrency}, #{indicatorData,jdbcType=OTHER,typeHandler=com.example.financialindicatordaemon.typehandler.IndicatorDataListTypeHandler})
    </insert>

    <resultMap id="indicatorDataWrapperResultMap" type="com.example.financialindicatordaemon.dto.IndicatorDataWrapper">
        <result column="indicator_values" property="indicatorValues"
                typeHandler="com.example.financialindicatordaemon.typehandler.IndicatorDataListTypeHandler"/>
    </resultMap>

    <select id="findLatestByCoinAndCurrency" resultMap="indicatorDataWrapperResultMap">
        SELECT indicator_values
        FROM crypto_data.indicator_data
        WHERE symbol = #{symbol}
          AND conversion_currency = #{conversionCurrency}
    </select>
</mapper>
