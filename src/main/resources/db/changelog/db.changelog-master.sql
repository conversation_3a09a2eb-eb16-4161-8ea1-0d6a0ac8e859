--liquibase formatted sql

--changeset financial_daemon:6
CREATE EXTENSION IF NOT EXISTS hstore;

--changeset financial_daemon:1
CREATE SCHEMA IF NOT EXISTS crypto_data;

--changeset financial_daemon:2
CREATE TABLE crypto_data.cmc_mappings
(
    cryptocurrency_id     INTEGER,
    symbol                VARCHAR(20)  NOT NULL,
    name                  VA<PERSON>HA<PERSON>(255) NOT NULL,
    slug                  VARCHAR(255) NOT NULL,
    rank                  INTEGER,
    is_active             BOOLEAN   DEFAULT FALSE,
    first_historical_data TIMESTAMP,
    last_historical_data  TIMESTAMP,
    platform              jsonb,
    created_at            TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at            TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

--changeset financial_daemon:3
CREATE TABLE crypto_data.cmc_candle_data
(
    symbol              VARCHAR(20),
    conversion_currency VARCHAR(3),
    time_open           TIMESTAMP,
    time_close          TIMESTAMP,
    time_high           TIMESTAMP,
    time_low            TIMESTAMP,
    quote               jsonb
);

--changeset financial_daemon:4
CREATE TABLE crypto_data.indicator_data
(
    symbol              VARCHAR(20) UNIQUE,
    conversion_currency VARCHAR(3) UNIQUE,
    indicator_values    jsonb
);

--changeset financial_daemon:5
CREATE TABLE crypto_data.statistics
(
    cryptocurrency_id INTEGER,
    date_calculated   DATE,
    currency_type     VARCHAR(3),
    statistics_data   jsonb,
    created_at        TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
